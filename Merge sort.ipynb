def mergesort (arr : list, left : int, right : int):
    if left >= right:
        return
    middle = (left + right) // 2  # Correct middle calculation using integer division

    mergesort(arr, left, middle)
    mergesort(arr, middle + 1, right)

    print(arr[left : middle + 1])  # Include middle in left subarray
    print(arr[middle + 1: right + 1])  # Include right in right subarray

test_array = [67,31,45,56,5]

mergesort(test_array, 0, len(test_array) - 1)